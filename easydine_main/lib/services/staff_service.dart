import 'dart:convert';
import 'dart:ffi';
import 'package:easydine_main/config/env_config.dart';
import 'package:easydine_main/utils/http_client.dart';
import '../models/staff_model.dart';

class StaffService {
  final String baseUrl = EnvConfig.apiBaseUrl;

  // Store staff list for PIN verification
  List<StaffModel> staffList = [];

  Future<List<StaffModel>> fetchBranchStaffs(String? branchId) async {
    print('🔍 StaffService: Starting to fetch staff for branch: $branchId');

    if (branchId == null || branchId.isEmpty) {
      print('❌ StaffService: Branch ID is null or empty');
      throw Exception('Branch ID is required to fetch staff');
    }

    final url = '$baseUrl/staff-manage/view_all/$branchId';
    print('🌐 StaffService: Making request to: $url');

    try {
      // Add delay to make request visible in network tab
      await Future.delayed(const Duration(seconds: 2));

      final response = await HttpClientService.get(url);
      print('📡 StaffService: Response status: ${response.statusCode}');
      print('📡 StaffService: Response headers: ${response.headers}');
      print('📡 StaffService: Response body: ${response.body}');

      if (response.statusCode == 200) {
        final responseBody = jsonDecode(response.body);
        print('✅ StaffService: Successfully decoded response');

        // Check if response has the expected structure
        if (responseBody is Map<String, dynamic>) {
          if (responseBody['success'] == true && responseBody['data'] != null) {
            final List<dynamic> data = responseBody['data'];
            print('✅ StaffService: Found ${data.length} staff members');
            return data.map((e) => StaffModel.fromJson(e)).toList();
          } else {
            print('❌ StaffService: API returned success=false or no data');
            print('❌ StaffService: Response: $responseBody');
            throw Exception(
                'API returned error: ${responseBody['message'] ?? 'Unknown error'}');
          }
        } else if (responseBody is List) {
          // Direct list response
          print(
              '✅ StaffService: Found ${responseBody.length} staff members (direct list)');
          return responseBody.map((e) => StaffModel.fromJson(e)).toList();
        } else {
          print('❌ StaffService: Unexpected response format');
          throw Exception('Unexpected response format');
        }
      } else {
        print('❌ StaffService: HTTP error ${response.statusCode}');
        print('❌ StaffService: Error body: ${response.body}');
        throw Exception(
            'Failed to fetch staffs: HTTP ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      print('💥 StaffService: Exception occurred: $e');
      rethrow;
    }
  }

  Future<List<StaffModel>> fetchBranchClockedInStaffs(String? branchId) async {
    final url = '$baseUrl/staff-manage/viewAllClockedInStaff/$branchId';
    final response = await HttpClientService.get(url);
    final responseBody = jsonDecode(response.body);
    if (responseBody is Map<String, dynamic>) {
      if (responseBody['success'] == true && responseBody['data'] != null) {
        final List<dynamic> data = responseBody['data'];
        print('✅ StaffService: Found ${data.length} staff members');
        return data.map((e) => StaffModel.fromJson(e)).toList();
      } else {
        print('❌ StaffService: API returned success=false or no data');
        print('❌ StaffService: Response: $responseBody');
        throw Exception(
            'API returned error: ${responseBody['message'] ?? 'Unknown error'}');
      }
    } else if (responseBody is List) {
      // Direct list response
      print(
          '✅ StaffService: Found ${responseBody.length} staff members (direct list)');
      return responseBody.map((e) => StaffModel.fromJson(e)).toList();
    } else {
      print('❌ StaffService: Unexpected response format');
      throw Exception('Unexpected response format');
    }
  }

  // PIN verification for dashboard access (check-in)
  Future<StaffModel?> verifyStaffPin(
    String staffId,
    String pin,
  ) async {
    print('🔄 StaffService: Verifying PIN for dashboard access (check-in)');
    print('🔄 StaffService: Staff cache size: ${staffList.length}');

    // Debug: Print staff IDs in cache
    if (staffList.isNotEmpty) {
      print(
          '🔄 StaffService: Staff IDs in cache: ${staffList.map((s) => s.id).join(', ')}');
    } else {
      print(
          '❌ StaffService: Staff cache is empty! This will cause PIN verification to fail.');
    }

    try {
      // Try attendance check-in for PIN verification
      final url = '$baseUrl/attendance/check-in/$staffId';
      final now = DateTime.now();

      final response = await HttpClientService.post(
        url,
        body: jsonEncode({
          'pin': int.parse(pin),
          'checkInTime': _formatTime(now),
          'date': _formatDate(now),
        }),
      );

      print(
          '🔄 StaffService: PIN verification response status: ${response.statusCode}');
      print(
          '🔄 StaffService: PIN verification response body: ${response.body}');

      if (response.statusCode == 201 || response.statusCode == 200) {
        // PIN correct and attendance updated
        print('✅ StaffService: PIN correct - attendance check-in successful');

        final responseData = jsonDecode(response.body);
        if (responseData is Map<String, dynamic> &&
            responseData['success'] == true &&
            responseData['data'] != null) {
          final attendanceData = responseData['data'];
          print('✅ StaffService: Attendance data: $attendanceData');

          // Create a simplified StaffModel from attendance response
          // Find the full staff data from cached list
          final staffId = attendanceData['staffId'];
          final fullStaff = staffList.firstWhere(
            (s) => s.id == staffId,
            orElse: () => throw Exception('Staff member not found in cache'),
          );

          print(
              '✅ StaffService: Using full staff data from cache: ${fullStaff.name}');
          return fullStaff;
        }
      } else if (response.statusCode == 409) {
        // PIN correct but already checked in - this is fine for dashboard access
        print('✅ StaffService: PIN correct - staff already checked in (409)');

        // Find staff from cached list since PIN is verified
        final staff = staffList.firstWhere(
          (s) => s.id == staffId,
          orElse: () => throw Exception('Staff member not found in cache'),
        );
        return staff;
      } else {
        // PIN incorrect or other error
        print(
            '❌ StaffService: PIN verification failed with status ${response.statusCode}');
        return null;
      }
    } catch (e) {
      print('❌ StaffService: PIN verification error: $e');
      throw Exception('Failed to verify PIN: $e');
    }

    return null;
  }

  // PIN verification for clock operations only
  Future<bool> verifyStaffPinForClock(
      String staffId, String pin, bool isClockIn) async {
    print(
        '🔄 StaffService: Verifying PIN for ${isClockIn ? 'clock-in' : 'clock-out'}');

    if (isClockIn) {
      return await clockInStaff(staffId, pin);
    } else {
      return await clockOutStaff(staffId, pin);
    }
  }

  // Update staff list when fetched
  Future<List<StaffModel>> fetchBranchStaffsWithCache(String? branchId) async {
    final staff = await fetchBranchStaffs(branchId);
    staffList = staff; // Cache for PIN verification
    return staff;
  }

  Future<StaffModel?> checkInStaff(
    String staffId,
    String pin,
  ) async {
    final url = '$baseUrl/attendance/check-in/$staffId';
    final now = DateTime.now();

    print('🔄 StaffService: Attempting check-in for staff $staffId');
    print('🔄 StaffService: Check-in URL: $url');
    print('🔄 StaffService: Staff cache size: ${staffList.length}');

    // If cache is empty, we'll handle the 409 response gracefully
    if (staffList.isEmpty) {
      print(
          '⚠️ StaffService: Staff cache is empty! Will handle 409 response by creating a minimal staff object.');
    }

    // Debug: Print staff IDs in cache
    if (staffList.isNotEmpty) {
      print(
          '🔄 StaffService: Staff IDs in cache: ${staffList.map((s) => s.id).join(', ')}');
    } else {
      print(
          '❌ StaffService: Staff cache is still empty! Will try to handle 409 response gracefully.');
    }

    final response = await HttpClientService.post(
      url,
      body: jsonEncode({
        'pin': int.parse(pin),
        'checkInTime': _formatTime(now),
        'date': _formatDate(now),
      }),
    );

    print('🔄 StaffService: Check-in response status: ${response.statusCode}');
    print('🔄 StaffService: Check-in response body: ${response.body}');

    // Handle 409 status first (already checked in)
    if (response.statusCode == 409) {
      print(
          '✅ StaffService: Staff already checked in (409) - creating session for dashboard access');

      // Try to find staff from cached list, if not found create minimal staff object
      StaffModel? staff;
      try {
        staff = staffList.firstWhere((s) => s.id == staffId);
        print('✅ StaffService: Found staff in cache: ${staff.name}');
      } catch (e) {
        print(
            '⚠️ StaffService: Staff not found in cache, creating minimal staff object');
        // Create a minimal staff object since PIN was verified by backend (409 response)
        staff = StaffModel(
          id: staffId,
          firstName: 'Staff',
          middleName: '', // Set empty string instead of null
          lastName: 'Member',
          emailAddress: '',
          phoneNumber: '',
          address: '',
          profileUrl: '',
          branches: [],
          staffDaysAvailable: StaffDaysAvailable.fromJson({}),
          defaultShiftTiming: DefaultShiftTiming.fromJson({}),
          staffCertifications: [],
          role: 'Staff',
        );
      }

      return staff;
    } else if (response.statusCode == 200 || response.statusCode == 201) {
      print('✅ StaffService: Check-in successful');

      try {
        final responseData = jsonDecode(response.body);

        // Check if response has the expected structure
        if (responseData is Map<String, dynamic> &&
            responseData['success'] == true &&
            responseData['data'] != null) {
          final attendanceData = responseData['data'];
          print('✅ StaffService: Attendance data: $attendanceData');

          // Find the full staff data from cached list
          final staffId = attendanceData['staffId'];
          final fullStaff = staffList.firstWhere(
            (s) => s.id == staffId,
            orElse: () => throw Exception('Staff member not found in cache'),
          );

          print(
              '✅ StaffService: Using full staff data from cache: ${fullStaff.name}');
          return fullStaff;
        } else {
          print('❌ StaffService: Invalid response structure');
          throw Exception('Invalid response structure');
        }
      } catch (e) {
        print('❌ StaffService: Error parsing response: $e');
        throw Exception('Failed to parse check-in response: $e');
      }
    } else {
      print(
          '❌ StaffService: Check-in failed with status ${response.statusCode}');
      throw Exception('Failed to check in staff: ${response.statusCode}');
    }
  }

  Future<bool> checkOutStaff(
    String staffId,
    String pin,
  ) async {
    final url = '$baseUrl/attendance/check-out/$staffId';
    final now = DateTime.now();
    final response = await HttpClientService.post(
      url,
      body: jsonEncode({
        'pin': int.parse(pin),
        'checkOutTime': _formatTime(now),
        'date': _formatDate(now),
      }),
    );
    if (response.statusCode == 200 || response.statusCode == 201) {
      return true;
    } else {
      throw Exception('Failed to Check out staff: ${response.statusCode}');
    }
  }

  Future<bool> clockInStaff(
    String staffId,
    String pin,
  ) async {
    final url = '$baseUrl/attendance/clock-in/$staffId';
    final now = DateTime.now();

    print('🔄 StaffService: Attempting clock-in for staff $staffId');
    print('🔄 StaffService: Clock-in URL: $url');

    try {
      final response = await HttpClientService.post(
        url,
        body: jsonEncode({
          'pin': int.parse(pin),
          'clockInTime': _formatTime(now),
          'date': _formatDate(now),
        }),
      );

      print(
          '📡 StaffService: Clock-in response status: ${response.statusCode}');
      print('📡 StaffService: Clock-in response body: ${response.body}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        print('✅ StaffService: Clock-in successful');
        return true;
      } else {
        // Parse error response
        String errorMessage = 'Failed to clock in staff';
        try {
          final errorBody = jsonDecode(response.body);
          if (errorBody is Map<String, dynamic> &&
              errorBody['message'] != null) {
            errorMessage = errorBody['message'];
          }
        } catch (e) {
          print('❌ StaffService: Could not parse error response: $e');
        }

        print('❌ StaffService: Clock-in failed: $errorMessage');
        throw Exception(errorMessage);
      }
    } catch (e) {
      print('💥 StaffService: Clock-in exception: $e');
      rethrow;
    }
  }

  Future<bool> clockOutStaff(
    String staffId,
    String pin,
  ) async {
    final url = '$baseUrl/attendance/clock-out/$staffId';
    final now = DateTime.now();

    print('🔄 StaffService: Attempting clock-out for staff $staffId');
    print('🔄 StaffService: Clock-out URL: $url');

    try {
      final response = await HttpClientService.post(
        url,
        body: jsonEncode({
          'pin': int.parse(pin),
          'clockOutTime': _formatTime(now),
          'date': _formatDate(now),
        }),
      );

      print(
          '📡 StaffService: Clock-out response status: ${response.statusCode}');
      print('📡 StaffService: Clock-out response body: ${response.body}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        print('✅ StaffService: Clock-out successful');
        return true;
      } else {
        // Parse error response
        String errorMessage = 'Failed to clock out staff';
        try {
          final errorBody = jsonDecode(response.body);
          if (errorBody is Map<String, dynamic> &&
              errorBody['message'] != null) {
            errorMessage = errorBody['message'];
          }
        } catch (e) {
          print('❌ StaffService: Could not parse error response: $e');
        }

        print('❌ StaffService: Clock-out failed: $errorMessage');
        throw Exception(errorMessage);
      }
    } catch (e) {
      print('💥 StaffService: Clock-out exception: $e');
      rethrow;
    }
  }

  String _formatTime(DateTime dt) {
    return dt.toLocal().toIso8601String().substring(11, 16); // HH:mm
  }

  String _formatDate(DateTime dt) {
    return dt.toIso8601String().substring(0, 10); // yyyy-MM-dd
  }
}
