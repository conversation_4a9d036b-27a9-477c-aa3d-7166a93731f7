import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:sizer/sizer.dart';

import '../blocs/pos/pos_bloc.dart';
import '../blocs/pos/pos_event.dart';

class CartHeader extends StatelessWidget {
  final String? tableNumber;
  final String orderId;
  final String orderType;

  const CartHeader({
    super.key,
    this.tableNumber,
    required this.orderId,
    required this.orderType,
  });

  @override
  Widget build(BuildContext context) {
    // Get display text based on order type
    String locationDisplay = orderType.toLowerCase() == "takeaway"
        ? "Takeaway"
        : orderType.toLowerCase() == "delivery"
            ? "Delivery"
            : "Table: $tableNumber";

    return Container(
      padding: EdgeInsets.symmetric(vertical: 0.15.h, horizontal: 1.w),
      child: Column(
        children: [
          Row(
            children: [
              Text(
                locationDisplay,
                style: GoogleFonts.dmSans(
                  fontSize: 6.sp,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const Spacer(),
              IconButton(
                icon: const Icon(Icons.clear_all, color: Colors.red),
                onPressed: () {
                  context.read<POSBloc>().add(ClearCart());
                },
              ),
            ],
          ),
          Row(
            children: [
              //hint to swipe left to duplicate right to customize show icons with text
              const Icon(Icons.arrow_back_ios, color: Colors.white),

              Text(
                'To Customize',
                style: GoogleFonts.dmSans(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const Spacer(),
              //hint to swipe left to duplicate right to customize show icons with text

              Text(
                'To Duplicate',
                style: GoogleFonts.dmSans(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const Icon(Icons.arrow_forward_ios, color: Colors.white),
            ],
          )
        ],
      ),
    );
  }
}
