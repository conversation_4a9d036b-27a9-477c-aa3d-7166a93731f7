import 'dart:convert';

import 'package:easydine_main/models/cartItem.dart';
import 'package:easydine_main/models/cart_models.dart';
import 'package:easydine_main/models/customization_models.dart';
import 'package:easydine_main/services/menu_service.dart';
import 'package:easydine_main/services/cart_service.dart';
import 'package:easydine_main/services/customization_service.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/foundation.dart';

import '../cart/cart_bloc.dart';
import '../cart/cart_event.dart' as cart_events;
import '../session/session_bloc.dart';
import 'pos_event.dart';
import 'pos_state.dart';

class POSBloc extends Bloc<POSEvent, POSState> {
  final CartBloc cartBloc;
  final SessionBloc sessionBloc;

  POSBloc({required this.cartBloc, required this.sessionBloc})
      : super(const POSState()) {
    on<CategorySelected>(_onCategorySelected);
    on<SearchMenuItems>(_onSearchMenuItems);
    on<AddToCart>(_onAddToCart);
    on<AddCustomizedItemToCart>(_onAddCustomizedItemToCart);
    on<UpdateCartItemQuantity>(_onUpdateCartItemQuantity);
    on<RemoveFromCart>(_onRemoveFromCart);
    on<ClearCart>(_onClearCart);
    on<PlaceOrder>(_onPlaceOrder);
    on<UpdateOrderPriority>(_onUpdateOrderPriority);
    on<SyncWithServerCart>(_onSyncWithServerCart);
    on<LoadMenuItems>(_onLoadMenuItems);
    on<AddToServerCart>(_onAddToServerCart);
    on<RemoveFromServerCart>(_onRemoveFromServerCart);
    on<ClearServerCart>(_onClearServerCart);
    on<SyncCartWithServer>(_onSyncCartWithServer);
    on<InitializePOS>(_onInitializePOS);
    on<LoadExistingCart>(_onLoadExistingCart);
  }

  void _onCategorySelected(CategorySelected event, Emitter<POSState> emit) {
    emit(state.copyWith(
      selectedCategory: event.category,
      searchQuery: '',
      filteredItems: [],
    ));
  }

  void _onSearchMenuItems(SearchMenuItems event, Emitter<POSState> emit) {
    final query = event.query.toLowerCase();

    if (query.isEmpty) {
      emit(state.copyWith(
        searchQuery: '',
        filteredItems: [],
        selectedCategory: null,
      ));
      return;
    }

    final allItems = MenuService.getAllMenuItems();
    final filteredItems = allItems.where((item) {
      return item.name.toLowerCase().contains(query) ||
          item.description.toLowerCase().contains(query) ||
          item.category.toLowerCase().contains(query);
    }).toList();

    emit(state.copyWith(
      searchQuery: query,
      filteredItems: filteredItems,
      selectedCategory: null,
    ));
  }

  Future<void> _onAddToCart(AddToCart event, Emitter<POSState> emit) async {
    try {
      debugPrint('🛒 POSBloc: Adding item to cart...');
      debugPrint('🛒 POSBloc: Item ID: ${event.id}');
      debugPrint('🛒 POSBloc: Item name: ${event.name}');
      debugPrint('🛒 POSBloc: Item price: ${event.price}');
      debugPrint(
          '🛒 POSBloc: Event customization data: ${event.customization}');

      // For standard add to cart (no customization)
      final request = AddItemToCartRequest(
        quantity: 1,
        dishId: event.id,
        type: "standard",
        notes: event.customization?['notes'],
      );

      debugPrint('🛒 POSBloc: Standard item request: ${request.toJson()}');
      cartBloc.add(cart_events.AddItemToCart(request: request));

      debugPrint('✅ POSBloc: Item add request sent to CartBloc');
    } catch (e) {
      debugPrint('❌ POSBloc: Error adding item to cart: $e');
      emit(state.copyWith(error: 'Failed to add item to cart'));
    }
  }

  /// Handle adding customization to an existing cart item
  Future<void> _onAddCustomizedItemToCart(
      AddCustomizedItemToCart event, Emitter<POSState> emit) async {
    try {
      debugPrint('🛒 POSBloc: Adding customization to cart item...');
      debugPrint('🛒 POSBloc: Cart Item ID: ${event.cartItemId}');
      debugPrint(
          '🛒 POSBloc: Customization data: ${jsonEncode(event.customizationData)}');

      // Send the customization update directly to the endpoint
      final success = await CartService.updateItem(
          event.cartItemId, event.customizationData);

      if (success) {
        debugPrint(
            '✅ POSBloc: Successfully updated cart item with customization');
        // Refresh the cart to show updated item
        cartBloc.add(cart_events.RefreshCart());
      } else {
        debugPrint('❌ POSBloc: Failed to update cart item with customization');
        emit(state.copyWith(error: 'Failed to add customization to cart item'));
      }
    } catch (e) {
      debugPrint('❌ POSBloc: Error adding customization to cart item: $e');
      emit(state.copyWith(error: 'Failed to add customization to cart item'));
    }
  }

  void _onUpdateCartItemQuantity(
      UpdateCartItemQuantity event, Emitter<POSState> emit) {
    try {
      debugPrint('🛒 POSBloc: Updating cart item quantity via CartBloc...');

      // Update quantity via CartBloc
      cartBloc.add(cart_events.UpdateCartItemQuantity(
        cartItemId: event.id,
        quantity: event.quantity,
      ));
      final req= CartService.updateItemQuantity(
        event.id,
        event.quantity,
      );

      debugPrint('✅ POSBloc: Quantity update request sent to CartBloc');
    } catch (e) {
      debugPrint('❌ POSBloc: Error updating cart item quantity: $e');
      emit(state.copyWith(error: 'Failed to update item quantity'));
    }
  }

  void _onRemoveFromCart(RemoveFromCart event, Emitter<POSState> emit) {
    try {
      debugPrint('🛒 POSBloc: Removing item from cart via CartBloc...');

      // Remove item via CartBloc
      cartBloc.add(cart_events.RemoveCartItem(cartItemId: event.id));

      debugPrint('✅ POSBloc: Item removal request sent to CartBloc');
    } catch (e) {
      debugPrint('❌ POSBloc: Error removing item from cart: $e');
      emit(state.copyWith(error: 'Failed to remove item from cart'));
    }
  }

  void _onClearCart(ClearCart event, Emitter<POSState> emit) {
    try {
      debugPrint('🛒 POSBloc: Clearing cart via CartBloc...');

      // Clear cart via CartBloc
      cartBloc.add(cart_events.ClearCart());

      debugPrint('✅ POSBloc: Cart clear request sent to CartBloc');
    } catch (e) {
      debugPrint('❌ POSBloc: Error clearing cart: $e');
      emit(state.copyWith(error: 'Failed to clear cart'));
    }
  }

  Future<void> _onPlaceOrder(PlaceOrder event, Emitter<POSState> emit) async {
    emit(state.copyWith(isProcessing: true));

    try {
      debugPrint('🛒 POSBloc: Placing order via CartBloc...');
      debugPrint('🛒 POSBloc: Order type: ${event.orderType}');
      debugPrint('🛒 POSBloc: Table number: ${event.tableNumber}');

      // Get waiter ID from session bloc
      final waiterId = sessionBloc.state.waiterId;
      if (waiterId == null) {
        debugPrint('❌ POSBloc: No waiter ID found in session');
        emit(state.copyWith(
          isProcessing: false,
          error: 'No waiter session found. Please log in again.',
        ));
        return;
      }

      debugPrint('🛒 POSBloc: Using waiter ID from session: $waiterId');

      // Get order types to map the order type string to ID
      String? orderTypeId;
      String? tableId;

      // Check if this is a takeaway order
      final isTakeaway = event.orderType.toLowerCase() == 'takeaway';

      if (isTakeaway) {
        debugPrint(
            '🛒 POSBloc: Processing takeaway order - fetching order types...');

        // For takeaway, get the takeaway order type ID and don't send table ID
        final orderTypes = await CustomizationService.getOrderTypes();
        if (orderTypes != null) {
          final takeawayOrderType = orderTypes.firstWhere(
            (orderType) =>
                orderType.reservedName.toLowerCase().contains('takeaway'),
            orElse: () => throw Exception('Takeaway order type not found'),
          );
          orderTypeId = takeawayOrderType.orderTypeId;
          tableId = null; // Don't send table ID for takeaway
          debugPrint('🛒 POSBloc: Found takeaway order type ID: $orderTypeId');
        } else {
          debugPrint('❌ POSBloc: Failed to fetch order types');
          emit(state.copyWith(
            isProcessing: false,
            error: 'Failed to fetch order types',
          ));
          return;
        }
      } else {
        // For dine-in orders, use the provided order type and table number
        orderTypeId = event.orderType;
        tableId = event.tableNumber;
        debugPrint(
            '🛒 POSBloc: Processing dine-in order with table ID: $tableId');
      }

      // Create confirm cart request
      final request = ConfirmCartRequest(
        assignedWaiterId: waiterId,
        tableId: tableId,
        orderTypeId: orderTypeId,
      );

      debugPrint('🛒 POSBloc: Confirm cart request: ${request.toJson()}');

      // Confirm cart (place order) via CartBloc
      cartBloc.add(cart_events.ConfirmCart(request: request));

      emit(state.copyWith(
        isProcessing: false,
        currentOrderId: event.orderId,
        currentPriority: event.priority,
      ));

      debugPrint('✅ POSBloc: Order placement request sent to CartBloc');
    } catch (e) {
      debugPrint('❌ POSBloc: Error placing order: $e');
      emit(state.copyWith(
        isProcessing: false,
        error: 'Failed to place order: $e',
      ));
    }
  }

  void _onUpdateOrderPriority(
      UpdateOrderPriority event, Emitter<POSState> emit) {
    emit(state.copyWith(currentPriority: event.priority));
  }

  /// Sync local cart with server cart
  Future<void> _onSyncWithServerCart(
      SyncWithServerCart event, Emitter<POSState> emit) async {
    try {
      debugPrint('🔄 POSBloc: Syncing with server cart...');
      final serverCart = await CartService.getOrCreateCart();

      if (serverCart != null) {
        // Convert server cart items to local cart items for display
        final localCartItems = <CartItem>[];

        for (final serverItem in serverCart.items) {
          // Create a simplified local cart item for display
          // Note: This is a basic conversion - you may need to enhance this
          // based on your specific requirements for displaying server cart items
          localCartItems.add(CartItem(
            id: serverItem.id,
            name: serverItem
                .dishId, // You might want to fetch dish name from menu
            price: 0.0, // You might want to fetch price from menu
            quantity: serverItem.quantity,
            customization: {
              'notes': serverItem.notes,
              'type': serverItem.type,
              'allergyIds': serverItem.allergyIds,
              'dishAddons':
                  serverItem.dishAddons.map((e) => e.toJson()).toList(),
              'dishExtras':
                  serverItem.dishExtras.map((e) => e.toJson()).toList(),
              'dishSides': serverItem.dishSides.map((e) => e.toJson()).toList(),
              'dishBeverages':
                  serverItem.dishBeverages.map((e) => e.toJson()).toList(),
              'dishDesserts':
                  serverItem.dishDesserts.map((e) => e.toJson()).toList(),
            },
          ));
        }

        // Cart items are now managed by CartBloc, no need to update local state
        debugPrint(
            '✅ POSBloc: Synced ${localCartItems.length} items from server cart');
      }
    } catch (e) {
      debugPrint('❌ POSBloc: Error syncing with server cart: $e');
      emit(state.copyWith(error: 'Failed to sync with server cart'));
    }
  }

  /// Load menu items from server
  Future<void> _onLoadMenuItems(
      LoadMenuItems event, Emitter<POSState> emit) async {
    try {
      debugPrint('📋 POSBloc: Loading menu items...');
      // This would typically load menu items and update the state
      // For now, we'll just clear any existing filters
      emit(state.copyWith(
        selectedCategory: "All",
        searchQuery: '',
        filteredItems: [],
      ));
    } catch (e) {
      debugPrint('❌ POSBloc: Error loading menu items: $e');
      emit(state.copyWith(error: 'Failed to load menu items'));
    }
  }

  /// Add item directly to server cart
  Future<void> _onAddToServerCart(
      AddToServerCart event, Emitter<POSState> emit) async {
    try {
      debugPrint('🛒 POSBloc: Adding item to server cart...');
      final request = AddItemToCartRequest(
        quantity: event.quantity,
        dishId: event.dishId,
        type: event.type,
        notes: event.notes,
        allergyIds: event.allergyIds,
      );

      final success = await CartService.addItemToCart(request);
      if (success) {
        debugPrint('✅ POSBloc: Successfully added item to server cart');
        // Optionally sync local cart with server
        add(const SyncCartWithServer());
      } else {
        debugPrint('❌ POSBloc: Failed to add item to server cart');
        emit(state.copyWith(error: 'Failed to add item to cart'));
      }
    } catch (e) {
      debugPrint('❌ POSBloc: Error adding item to server cart: $e');
      emit(state.copyWith(error: 'Error adding item to cart: $e'));
    }
  }

  /// Remove item from server cart
  Future<void> _onRemoveFromServerCart(
      RemoveFromServerCart event, Emitter<POSState> emit) async {
    try {
      debugPrint('🗑️ POSBloc: Removing item from server cart...');
      final success = await CartService.deleteItemFromCart(event.cartItemId);

      if (success) {
        debugPrint('✅ POSBloc: Successfully removed item from server cart');
        // Sync local cart with server
        add(const SyncCartWithServer());
      } else {
        debugPrint('❌ POSBloc: Failed to remove item from server cart');
        emit(state.copyWith(error: 'Failed to remove item from cart'));
      }
    } catch (e) {
      debugPrint('❌ POSBloc: Error removing item from server cart: $e');
      emit(state.copyWith(error: 'Error removing item from cart: $e'));
    }
  }

  /// Clear server cart
  Future<void> _onClearServerCart(
      ClearServerCart event, Emitter<POSState> emit) async {
    try {
      debugPrint('🧹 POSBloc: Clearing server cart...');
      final success = await CartService.clearCart();

      if (success) {
        debugPrint('✅ POSBloc: Successfully cleared server cart');
        // Cart is now managed by CartBloc
      } else {
        debugPrint('❌ POSBloc: Failed to clear server cart');
        emit(state.copyWith(error: 'Failed to clear cart'));
      }
    } catch (e) {
      debugPrint('❌ POSBloc: Error clearing server cart: $e');
      emit(state.copyWith(error: 'Error clearing cart: $e'));
    }
  }

  /// Sync local cart with server cart
  Future<void> _onSyncCartWithServer(
      SyncCartWithServer event, Emitter<POSState> emit) async {
    try {
      debugPrint('🔄 POSBloc: Syncing local cart with server...');
      final serverCart = await CartService.getOrCreateCart();

      if (serverCart != null) {
        // Convert server cart items to local cart items for display
        final localCartItems = <CartItem>[];

        for (final serverItem in serverCart.items) {
          // Create a simplified local cart item for display
          localCartItems.add(CartItem(
            id: serverItem.id,
            name: serverItem
                .dishId, // You might want to fetch dish name from menu
            price: 0.0, // You might want to fetch price from menu
            quantity: serverItem.quantity,
            customization: {
              'notes': serverItem.notes,
              'type': serverItem.type,
              'allergyIds': serverItem.allergyIds,
              'dishAddons':
                  serverItem.dishAddons.map((e) => e.toJson()).toList(),
              'dishExtras':
                  serverItem.dishExtras.map((e) => e.toJson()).toList(),
              'dishSides': serverItem.dishSides.map((e) => e.toJson()).toList(),
              'dishBeverages':
                  serverItem.dishBeverages.map((e) => e.toJson()).toList(),
              'dishDesserts':
                  serverItem.dishDesserts.map((e) => e.toJson()).toList(),
            },
          ));
        }

        // Cart items are now managed by CartBloc, no need to update local state
        debugPrint(
            '✅ POSBloc: Synced ${localCartItems.length} items from server cart');
      }
    } catch (e) {
      debugPrint('❌ POSBloc: Error syncing with server cart: $e');
      emit(state.copyWith(error: 'Failed to sync with server cart'));
    }
  }

  /// Initialize POS screen - load existing cart
  Future<void> _onInitializePOS(
      InitializePOS event, Emitter<POSState> emit) async {
    try {
      debugPrint('🚀 POSBloc: Initializing POS screen...');

      // Load existing cart via CartBloc
      cartBloc.add(cart_events.LoadCart());

      debugPrint('✅ POSBloc: Cart load request sent to CartBloc');
    } catch (e) {
      debugPrint('❌ POSBloc: Error initializing POS: $e');
      emit(state.copyWith(error: 'Failed to initialize POS'));
    }
  }

  /// Load existing cart data
  Future<void> _onLoadExistingCart(
      LoadExistingCart event, Emitter<POSState> emit) async {
    try {
      debugPrint('🛒 POSBloc: Loading existing cart...');

      // Refresh cart data via CartBloc
      cartBloc.add(cart_events.RefreshCart());

      debugPrint('✅ POSBloc: Cart refresh request sent to CartBloc');
    } catch (e) {
      debugPrint('❌ POSBloc: Error loading existing cart: $e');
      emit(state.copyWith(error: 'Failed to load existing cart'));
    }
  }

  // Helper method to compare customizations
  bool _areCustomizationsEqual(
    Map<String, dynamic>? customization1,
    Map<String, dynamic>? customization2,
  ) {
    if (customization1 == null && customization2 == null) return true;
    if (customization1 == null || customization2 == null) return false;

    // Convert to string for deep comparison
    return jsonEncode(customization1) == jsonEncode(customization2);
  }
}
