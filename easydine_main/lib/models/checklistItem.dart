import 'package:flutter/material.dart';

class ChecklistItem {
  final String title;
  final String subtitle;
  final IconData icon;
  final List<String>? subitems;
  bool isCompleted;

  ChecklistItem({
    required this.title,
    required this.subtitle,
    required this.icon,
    this.subitems,
    this.isCompleted = false,
  });

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'subtitle': subtitle,
      'iconCodePoint': icon.codePoint,
      'iconFontFamily': icon.fontFamily,
      'iconFontPackage': icon.fontPackage,
      'subitems': subitems,
      'isCompleted': isCompleted,
    };
  }

  // Create from JSON
  factory ChecklistItem.fromJson(Map<String, dynamic> json) {
    return ChecklistItem(
      title: json['title'],
      subtitle: json['subtitle'],
      icon: IconData(
        json['iconCodePoint'],
        fontFamily: json['iconFontFamily'],
        fontPackage: json['iconFontPackage'],
      ),
      subitems:
      json['subitems'] != null ? List<String>.from(json['subitems']) : null,
      isCompleted: json['isCompleted'] ?? false,
    );
  }

  // Create a copy with updated properties
  ChecklistItem copyWith({
    String? title,
    String? subtitle,
    IconData? icon,
    List<String>? subitems,
    bool? isCompleted,
  }) {
    return ChecklistItem(
      title: title ?? this.title,
      subtitle: subtitle ?? this.subtitle,
      icon: icon ?? this.icon,
      subitems: subitems ?? this.subitems,
      isCompleted: isCompleted ?? this.isCompleted,
    );
  }
}
