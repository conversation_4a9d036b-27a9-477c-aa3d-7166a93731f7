import 'package:http_interceptor/http_interceptor.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';

import '../config/env_config.dart';

typedef TokenExpiredCallback = void Function();
TokenExpiredCallback? _globalTokenExpiredCallback;

void setTokenExpiredCallback(TokenExpiredCallback callback) {
  _globalTokenExpiredCallback = callback;
}

class CustomInterceptor implements InterceptorContract {
  @override
  bool shouldInterceptRequest() => true;

  @override
  bool shouldInterceptResponse() => true;

  @override
  Future<BaseRequest> interceptRequest({required BaseRequest request}) async {
    // Add standard headers
    request.headers["x-host"] = EnvConfig.xHost;
    request.headers["Content-Type"] = "application/json";

    final prefs = await SharedPreferences.getInstance();

    // Determine which token to use based on request URL
    final requestUrl = request.url.toString();
    String? token;

    // Check if this is an attendance operation (clock/check in/out)
    if (_isAttendanceOperation(requestUrl)) {
      // debugPrint(
      //     '🔑 HTTP Interceptor: Attendance operation - using manager token');
      token = prefs.getString('manager-token');
    } else if (_isStaffOperation(requestUrl)) {
      token = prefs.getString('staff-token');
      // debugPrint('🔑 HTTP Interceptor: Using staff-token for staff operation');
    } else if (_isCartOperation(requestUrl)) {
      token = prefs.getString('staff-token');
      // debugPrint(
      //     '🔑 HTTP Interceptor: Using staff token for cart operation');
    }


    else {
      token = prefs.getString('manager-token');
      // debugPrint(
      //     '🔑 HTTP Interceptor: Using manager token for manager operation');
    }

    if (token != null) {
      request.headers["Authorization"] = "Bearer $token";
      request.headers["Cookie"] = "access-token=$token";
    }

    return request;
  }

  bool _isAttendanceOperation(String url) {
    // Attendance operations (clock in/out, check in/out) use manager tokens for authorization
    return url.contains('/attendance/');
  }

  bool _isStaffOperation(String url) {
    // Define staff-specific operations that might use staff tokens
    // NOTE: attendance operations use manager tokens for authorization but generate staff tokens
    return url.contains('/staff/session') ||
        url.contains('/staff/dashboard') ||
        url.contains(
            '/attendance/check-in/') || // Staff check-in generates staff tokens
        url.contains(
            '/attendance/check-out/'); // Staff check-out may also generate staff tokens
  }

  bool _isCartOperation(String url) {
    return url.contains('/cart/') || url.contains('/order/');
  }

  @override
  Future<BaseResponse> interceptResponse(
      {required BaseResponse response}) async {
    // Check for 401 Unauthorized (token expired)
    if (response.statusCode == 401) {
      // debugPrint(
      //     '🚫 HTTP Interceptor: 401 Unauthorized - Token may be expired');
      // debugPrint('🚫 HTTP Interceptor: Request URL: ${response.request?.url}');
      // debugPrint('🚫 HTTP Interceptor: Response body: ${response.toString()}');

      final requestUrl = response.request?.url.toString() ?? '';
      final prefs = await SharedPreferences.getInstance();

      if (_isStaffOperation(requestUrl)) {
        // debugPrint(
        //     '🔍 HTTP Interceptor: 401 from staff operation - clearing staff token only');

        // Clear only staff token for staff operations
        final staffTokenBefore = prefs.getString('staff-token');
        if (staffTokenBefore != null) {
          await prefs.remove('staff-token');
          // debugPrint('🚫 HTTP Interceptor: Staff token cleared');
        }

        // Manager token remains untouched
        final managerToken = prefs.getString('manager-token');
        // debugPrint(
        //     '✅ HTTP Interceptor: Manager token preserved: ${managerToken != null ? 'EXISTS' : 'NULL'}');
      } else {
        // debugPrint(
        //     '🚫 HTTP Interceptor: 401 from manager operation - clearing manager token');

        // Clear manager token for manager operations
        final managerTokenBefore = prefs.getString('manager-token');
        if (managerTokenBefore != null) {
          await prefs.remove('manager-token');
          // debugPrint('🚫 HTTP Interceptor: Manager token cleared');
        }

        // Also clear legacy access-token for backward compatibility
        await prefs.remove('access-token');
        await prefs.remove('token-timestamp');

        // debugPrint(
        //     '🚫 HTTP Interceptor: Manager token cleared due to 401 response');

        // Trigger immediate token expiration callback
        if (_globalTokenExpiredCallback != null) {
          // debugPrint(
          //     '🔄 HTTP Interceptor: Triggering token expiration callback');
          _globalTokenExpiredCallback!();
        }
      }
    }

    // Check for and save new tokens from response
    if (response.headers.containsKey('set-cookie')) {
      final cookieHeader = response.headers['set-cookie'];
      if (cookieHeader != null) {
        final token = _extractTokenFromCookies(cookieHeader);
        if (token != null) {
          final prefs = await SharedPreferences.getInstance();
          final requestUrl = response.request?.url.toString() ?? '';

          // Determine if this is a manager login response
          if (requestUrl.contains('/tenant/login')) {
            // Manager login - save as manager token
            await prefs.setString('manager-token', token);
            await prefs.setString(
                'manager-token-timestamp', DateTime.now().toIso8601String());
            debugPrint(
                '🔐 HTTP Interceptor: New manager token saved with timestamp');
          } else if (_isStaffOperation(requestUrl)) {
            // Staff operation - save as staff token
            await prefs.setString('staff-token', token);
            await prefs.setString(
                'staff-token-timestamp', DateTime.now().toIso8601String());
            debugPrint(
                '🔐 HTTP Interceptor: New staff token saved with timestamp');

            // For staff check-in operations, also log the token details and verify storage
            if (requestUrl.contains('/attendance/check-in/')) {
              debugPrint(
                  '👤 HTTP Interceptor: Staff check-in token stored - ${token.substring(0, token.length > 30 ? 30 : token.length)}...');

              // Verify the token was stored correctly
              final storedStaffToken = prefs.getString('staff-token');
              final storedManagerToken = prefs.getString('manager-token');
              debugPrint(
                  '✅ HTTP Interceptor: Verification - Staff token stored: ${storedStaffToken != null ? 'YES' : 'NO'}');
              debugPrint(
                  '✅ HTTP Interceptor: Verification - Manager token preserved: ${storedManagerToken != null ? 'YES' : 'NO'}');

              if (storedStaffToken != null && storedManagerToken != null) {
                debugPrint(
                    '🎯 HTTP Interceptor: Both tokens successfully maintained');
              }
            }
          }

          // Also save in legacy location for backward compatibility
          await prefs.setString('access-token', token);
          await prefs.setString(
              'token-timestamp', DateTime.now().toIso8601String());
        } else {
          debugPrint(
              '🚫 HTTP Interceptor: No valid token extracted from cookies');
        }
      }
    }
    return response;
  }

  // Helper to extract token from cookies (handles multiple set-cookie headers)
  // Uses the same logic as AuthBloc for consistency
  String? _extractTokenFromCookies(dynamic cookieHeader) {
    if (cookieHeader == null) return null;

    debugPrint(
        '🍪 HTTP Interceptor: Raw cookie header type: ${cookieHeader.runtimeType}');
    debugPrint('🍪 HTTP Interceptor: Raw cookie header: $cookieHeader');

    List<String> cookieParts = [];

    // Handle different types of cookie headers (same as AuthBloc)
    if (cookieHeader is List<String>) {
      cookieParts = cookieHeader;
      debugPrint(
          '🍪 HTTP Interceptor: Cookie header is List<String> with ${cookieParts.length} items');
    } else if (cookieHeader is String) {
      // First try splitting by actual Set-Cookie boundaries
      if (cookieHeader.contains('set-cookie access-token=')) {
        final setCookiePattern = RegExp(r'set-cookie\s+access-token=([^;]+)');
        final matches = setCookiePattern.allMatches(cookieHeader);
        cookieParts =
            matches.map((match) => 'access-token=${match.group(1)}').toList();
      } else {
        // Handle normal case - split by newlines first
        final lines = cookieHeader.split('\n');
        for (final line in lines) {
          if (line.trim().contains('access-token=')) {
            cookieParts.add(line.trim());
          }
        }

        // If no newlines, check if it's a single line with multiple cookies
        if (cookieParts.isEmpty && cookieHeader.contains('access-token=')) {
          // Try to find multiple access-token patterns
          final tokenPattern =
              RegExp(r'access-token=([^,]*(?:,[^=]*)*?)(?=\s+\w+:|$)');
          final matches = tokenPattern.allMatches(cookieHeader);
          cookieParts =
              matches.map((match) => 'access-token=${match.group(1)}').toList();

          if (cookieParts.isEmpty) {
            // Fallback - just use the whole string
            cookieParts = [cookieHeader];
          }
        }
      }
      debugPrint(
          '🍪 HTTP Interceptor: Cookie header is String, found ${cookieParts.length} parts');
    } else {
      debugPrint(
          '🍪 HTTP Interceptor: Unknown cookie header type: ${cookieHeader.runtimeType}');
      return null;
    }

    // Extract all access-token values (same as AuthBloc)
    final accessTokens = <String>[];

    for (int i = 0; i < cookieParts.length; i++) {
      final part = cookieParts[i].trim();
      debugPrint('🍪 HTTP Interceptor: Processing cookie part $i: $part');

      // Find ALL occurrences of 'access-token=' in this part
      final accessTokenPattern = RegExp(r'access-token=([^;,]+)');
      final matches = accessTokenPattern.allMatches(part);

      for (final match in matches) {
        final tokenValue = match.group(1) ?? '';
        if (tokenValue.isNotEmpty) {
          accessTokens.add(tokenValue);
          debugPrint(
              '🍪 HTTP Interceptor: Extracted token ${accessTokens.length - 1}: ${tokenValue.substring(0, tokenValue.length.clamp(0, 30))}...');
        }
      }
    }

    debugPrint(
        '🍪 HTTP Interceptor: Found ${accessTokens.length} access-token cookies total');

    // Find the valid JWT token (should contain "Bearer" when decoded) - same as AuthBloc
    for (int i = accessTokens.length - 1; i >= 0; i--) {
      try {
        final decodedToken = Uri.decodeComponent(accessTokens[i]);
        debugPrint(
            '🍪 HTTP Interceptor: Decoded token $i: ${decodedToken.substring(0, decodedToken.length.clamp(0, 50))}...');

        // Skip the clearing token (usually just contains a dot or is very short)
        if (decodedToken.length > 10 && decodedToken.contains('Bearer')) {
          debugPrint(
              '✅ HTTP Interceptor: Using token $i as it contains Bearer');
          return decodedToken;
        } else {
          debugPrint(
              '❌ HTTP Interceptor: Skipping token $i: too short or no Bearer');
        }
      } catch (e) {
        debugPrint('❌ HTTP Interceptor: Failed to decode token $i: $e');
        continue;
      }
    }

    // Fallback: use the last token if no Bearer token found (same as AuthBloc)
    if (accessTokens.isNotEmpty) {
      try {
        final lastToken = Uri.decodeComponent(accessTokens.last);
        if (lastToken.isNotEmpty && lastToken != '.' && lastToken.length > 5) {
          debugPrint('🔄 HTTP Interceptor: Fallback: using last token');
          return lastToken;
        }
      } catch (e) {
        debugPrint('❌ HTTP Interceptor: Fallback failed: $e');
      }
    }

    debugPrint('❌ HTTP Interceptor: No valid token found');
    return null;
  }
}
